<!--
    Demo component to showcase the enhanced customer store tag management functionality
    This demonstrates how the store can be used to fetch and manage customer tags
-->
<script lang="ts">
    import { onMount } from 'svelte';
    import { customerStore, customerTags } from '$lib/stores/customerStore';
    import { <PERSON><PERSON>, <PERSON><PERSON>, Spinner } from 'flowbite-svelte';
    import { RefreshOutline } from 'flowbite-svelte-icons';

    // Mock access token for demo purposes
    const mockAccessToken = 'demo-token';

    // Subscribe to the customer tags store
    $: tagsData = $customerTags;

    // Demo function to simulate fetching tags
    async function fetchTags() {
        try {
            await customerStore.fetchCustomerTags(mockAccessToken);
        } catch (error) {
            console.error('Error fetching tags:', error);
        }
    }

    // Demo function to simulate setting tags manually
    function setDemoTags() {
        const demoTags = [
            { id: 1, name: 'VIP', color: 'gold', description: 'VIP Customer' },
            { id: 2, name: 'Regular', color: 'blue', description: 'Regular Customer' },
            { id: 3, name: 'New', color: 'green', description: 'New Customer' }
        ];
        customerStore.setCustomerTags(demoTags);
    }

    // Demo function to simulate an error
    function simulateError() {
        customerStore.setTagsError('Demo error: Failed to fetch tags');
        customerStore.setTagsLoading(false);
    }

    // Demo function to clear all data
    function clearData() {
        customerStore.setCustomerTags([]);
        customerStore.setTagsError(null);
        customerStore.setTagsLoading(false);
    }

    // Demo function to start polling (simulates customer polling with tag updates)
    function startPollingDemo() {
        const demoCustomerId = 123;
        customerStore.startPolling(demoCustomerId, mockAccessToken);
    }

    // Demo function to stop polling
    function stopPollingDemo() {
        const demoCustomerId = 123;
        customerStore.stopPolling(demoCustomerId);
    }

    onMount(() => {
        console.log('CustomerTagsDemo mounted');
    });
</script>

<div class="p-6 max-w-4xl mx-auto">
    <h1 class="text-2xl font-bold mb-6">Customer Store Tag Management Demo</h1>
    
    <!-- Control Buttons -->
    <div class="flex gap-4 mb-6">
        <Button on:click={fetchTags} disabled={tagsData.loading}>
            <RefreshOutline class="w-4 h-4 mr-2" />
            Fetch Tags (API)
        </Button>
        
        <Button on:click={setDemoTags} color="green">
            Set Demo Tags
        </Button>
        
        <Button on:click={simulateError} color="red">
            Simulate Error
        </Button>
        
        <Button on:click={clearData} color="light">
            Clear Data
        </Button>

        <Button on:click={startPollingDemo} color="purple">
            Start Polling Demo
        </Button>

        <Button on:click={stopPollingDemo} color="alternative">
            Stop Polling Demo
        </Button>
    </div>

    <!-- Loading State -->
    {#if tagsData.loading}
        <div class="flex items-center gap-2 mb-4">
            <Spinner size="4" />
            <span>Loading customer tags...</span>
        </div>
    {/if}

    <!-- Error State -->
    {#if tagsData.error}
        <Alert color="red" class="mb-4">
            <span class="font-medium">Error:</span> {tagsData.error}
        </Alert>
    {/if}

    <!-- Tags Display -->
    <div class="bg-white rounded-lg shadow p-4">
        <h2 class="text-lg font-semibold mb-4">Customer Tags ({tagsData.tags.length})</h2>
        
        {#if tagsData.tags.length > 0}
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {#each tagsData.tags as tag}
                    <div class="border rounded-lg p-3">
                        <div class="flex items-center gap-2 mb-2">
                            <div 
                                class="w-4 h-4 rounded-full" 
                                style="background-color: {tag.color || '#gray'}"
                            ></div>
                            <span class="font-medium">{tag.name}</span>
                        </div>
                        <div class="text-sm text-gray-600">
                            ID: {tag.id}
                        </div>
                        {#if tag.description}
                            <div class="text-sm text-gray-500 mt-1">
                                {tag.description}
                            </div>
                        {/if}
                    </div>
                {/each}
            </div>
        {:else if !tagsData.loading}
            <div class="text-gray-500 text-center py-8">
                No customer tags available. Click "Set Demo Tags" to see sample data.
            </div>
        {/if}
    </div>

    <!-- Store State Debug Info -->
    <div class="mt-6 bg-gray-100 rounded-lg p-4">
        <h3 class="text-md font-semibold mb-2">Store State Debug</h3>
        <pre class="text-sm overflow-auto">{JSON.stringify({
            tagsCount: tagsData.tags.length,
            loading: tagsData.loading,
            error: tagsData.error,
            tags: tagsData.tags
        }, null, 2)}</pre>
    </div>

    <!-- Usage Instructions -->
    <div class="mt-6 bg-blue-50 rounded-lg p-4">
        <h3 class="text-md font-semibold mb-2">How to Use in Components</h3>
        <div class="text-sm space-y-2">
            <p><strong>1. Import the store:</strong></p>
            <code class="bg-gray-200 px-2 py-1 rounded">import &#123; customerStore, customerTags &#125; from '$lib/stores/customerStore';</code>

            <p><strong>2. Subscribe to tags data:</strong></p>
            <code class="bg-gray-200 px-2 py-1 rounded">$: tagsData = $customerTags;</code>

            <p><strong>3. Fetch tags:</strong></p>
            <code class="bg-gray-200 px-2 py-1 rounded">await customerStore.fetchCustomerTags(accessToken);</code>

            <p><strong>4. Use in components:</strong></p>
            <code class="bg-gray-200 px-2 py-1 rounded">&#123;#each tagsData.tags as tag&#125; ... &#123;/each&#125;</code>
        </div>
    </div>

    <!-- Enhanced Polling Features -->
    <div class="mt-6 bg-green-50 rounded-lg p-4">
        <h3 class="text-md font-semibold mb-2">Enhanced Polling Features</h3>
        <div class="text-sm space-y-2">
            <p><strong>🔄 Automatic Tag Updates:</strong> When customer polling is active, tags are automatically refreshed every 5 minutes (configurable).</p>

            <p><strong>⚡ Smart Frequency:</strong> Customer data polls every 30 seconds, tags update every 5 minutes to reduce API load.</p>

            <p><strong>🛡️ Error Resilience:</strong> Tag fetch errors don't disrupt customer data polling - they're handled gracefully.</p>

            <p><strong>📊 Synchronized Data:</strong> Both customer information and available tags stay current during active sessions.</p>

            <p><strong>🎛️ Polling Control:</strong></p>
            <code class="bg-gray-200 px-2 py-1 rounded">customerStore.startPolling(customerId, accessToken);</code><br>
            <code class="bg-gray-200 px-2 py-1 rounded">customerStore.stopPolling(customerId);</code>
        </div>
    </div>
</div>
