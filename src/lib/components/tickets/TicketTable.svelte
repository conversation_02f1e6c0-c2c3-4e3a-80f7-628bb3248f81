<script lang="ts">
    import { t, language } from '$lib/stores/i18n';
    import { derived, writable } from 'svelte/store';
    import { createEventDispatcher } from 'svelte';
    import { goto } from '$app/navigation';

    import {
        TableBody,
        TableBodyCell,
        TableBodyRow,
        TableHead,
        TableHeadCell,
        Table
    } from 'flowbite-svelte';
    import { Tooltip } from 'flowbite-svelte';
    import {
        EditSolid,
        CaretDownSolid,
        CaretUpSolid
    } from 'flowbite-svelte-icons';
    import TransferTicketOwner from '$lib/components/UI/TransferTicketOwner.svelte';
    import ChangeTicketStatus from '$lib/components/UI/ChangeTicketStatus.svelte';
    import ChangeTicketPriority from '$src/lib/components/UI/ChangeTicketPriority.svelte';
    import Pagination from '$src/lib/components/UI/pagination.svelte';
    import LoadingSpinner from '$lib/components/common/LoadingSpinner.svelte';
    import {
        displayDate,
        timeAgo,
        getStatusClass,
        getPriorityClass,
        getSentimentClass,
        getSentimentIcon
    } from '$lib/utils';

    // Create event dispatcher
    const dispatch = createEventDispatcher();

    // Props for the component
    export let tickets = [];
    export let users = [];
    export let statuses = [];
    export let priorities = [];
    export let ticket_topics = [];
    export let loginUser = null;

    // Pagination state variables
    export let currentPage = 1;
    export let totalPages = 1;
    export let updateCurrentPage: (page: number) => void;

    // Add a prop for ordering and the sort handler
    export let ordering = '';
    export let handleSort = (column: string) => {};

    // Row selection and loading state
    let selectedTicketId: number | null = null;
    let isLoading = false;

    // Function to handle ticket refresh after actions
    function handleTicketRefresh() {
        dispatch('ticketRefresh');
    }

    // Handle row click for navigation
    async function handleRowClick(ticket: any) {
        selectedTicketId = ticket.id;
        isLoading = true;

        try {
            await goto(`/monitoring/${ticket.id}`);
        } catch (error) {
            console.error('Navigation error:', error);
            isLoading = false; // Reset loading state on error
        }
    }

    // Prevent action button clicks from triggering row selection
    function handleActionClick(event: Event) {
        event.stopPropagation();
    }

    // Map frontend column names to backend ordering field names (same as parent)
    const columnFieldMap = {
        'id': 'id',
        'status': 'status_id__id', 
        'priority': 'priority__id',
        'customer': 'customer_id__name',
        'agent': 'owner_id__name', 
        'updated_ago': 'updated_on',  // Frontend displays 'updated_ago' but sorts by 'updated_on'
        'created_on': 'created_on',
        'sentiment': 'latest_sentiment',
        'contact_channel': 'contact_channel'
    };

    // Helper function to show sort icon - make it reactive
    $: getCaretIcon = (column: string) => {
        const backendField = columnFieldMap[column] || column;
        if (ordering === backendField) {
            return CaretUpSolid; // First state. Ascending for all but time columns
        } else if (ordering === '-' + backendField) {
            return CaretDownSolid; // Second state. Descending for all but time columns
        }
        return null;
    };
</script>

<div id="ticket-table-wrapper" class="relative w-full">
    <!-- Loading overlay -->
    {#if isLoading}
        <!-- svelte-ignore a11y-click-events-have-key-events -->
        <!-- svelte-ignore a11y-no-static-element-interactions -->
        <div
            id="ticket-table-overlay"
            class="fixed inset-0 z-50 bg-black bg-opacity-50 transition-opacity duration-300"
        >
            <div class="flex h-full items-center justify-center">
                <div class="rounded-lg bg-white p-6 shadow-lg">
                    <div class="flex items-center gap-3">
                        <LoadingSpinner size="lg" />
                        <span class="text-lg font-medium text-gray-700">{t('loading')}</span>
                    </div>
                </div>
            </div>
        </div>
    {/if}

    <Table shadow class="table-fixed {isLoading ? 'pointer-events-none opacity-50' : ''}">
        <TableHead>
            <TableHeadCell class="w-[60px] cursor-pointer" on:click={() => handleSort('id')}>
                <div class="flex items-center justify-center">
                    {t('table_no')}
                    {#if getCaretIcon('id')}
                        <svelte:component this={getCaretIcon('id')} class="inline-block h-4 w-4 ml-1" />
                    {/if}
                </div>
            </TableHeadCell>
            <TableHeadCell class="w-[120px] cursor-pointer" on:click={() => handleSort('status')}>
                <div class="flex items-center justify-center">
                    {t('table_status')}
                    {#if getCaretIcon('status')}
                        <svelte:component this={getCaretIcon('status')} class="inline-block h-4 w-4 ml-1" />
                    {/if}
                </div>
            </TableHeadCell>

            <TableHeadCell class="w-[100px] cursor-pointer" on:click={() => handleSort('priority')}>
                <div class="flex items-center justify-center text-center">
                    {t('table_priority')}
                    {#if getCaretIcon('priority')}
                        <svelte:component this={getCaretIcon('priority')} class="inline-block h-4 w-4 ml-1" />
                    {/if}
                </div>
            </TableHeadCell>

            <TableHeadCell class="w-[90px] text-center cursor-pointer" on:click={() => handleSort('sentiment')}>
                <div class="flex items-center justify-center">
                    {t('table_sentiment')}
                    {#if getCaretIcon('sentiment')}
                        <svelte:component this={getCaretIcon('sentiment')} class="inline-block h-4 w-4 ml-1" />
                    {/if}
                </div>
            </TableHeadCell>

            <TableHeadCell class="w-[150px] cursor-pointer" on:click={() => handleSort('customer')}>
                <div class="flex items-center justify-start">
                    {t('table_customer')}
                    {#if getCaretIcon('customer')}
                        <svelte:component this={getCaretIcon('customer')} class="inline-block h-4 w-4 ml-1" />
                    {/if}
                </div>
            </TableHeadCell>

            <TableHeadCell class="w-[150px]">
                <div class="flex items-center justify-start">
                    {t('contact_channel')}
                    <!-- {#if getCaretIcon('contact_channel')}
                        <svelte:component this={getCaretIcon('contact_channel')} class="inline-block h-4 w-4 ml-1" />
                    {/if} -->
                </div>
            </TableHeadCell>

            <TableHeadCell class="w-[150px] cursor-pointer" on:click={() => handleSort('agent')}>
                <div class="flex items-center justify-start">
                    {t('table_agent')}
                    {#if getCaretIcon('agent')}
                        <svelte:component this={getCaretIcon('agent')} class="inline-block h-4 w-4 ml-1" />
                    {/if}
                </div>
            </TableHeadCell>

            <TableHeadCell class="w-[120px] cursor-pointer" on:click={() => handleSort('created_on')}>
                <div class="flex items-center justify-start">
                    {t('table_time')}
                    {#if getCaretIcon('created_on')}
                        <svelte:component this={getCaretIcon('created_on')} class="inline-block h-4 w-4 ml-1" />
                    {/if}
                </div>
            </TableHeadCell>

            <TableHeadCell class="w-[120px] cursor-pointer" on:click={() => handleSort('updated_ago')}>
                <div class="flex items-center justify-start">
                    {t('table_updated_ago')}
                    {#if getCaretIcon('updated_ago')}
                        <svelte:component this={getCaretIcon('updated_ago')} class="inline-block h-4 w-4 ml-1" />
                    {/if}
                </div>
            </TableHeadCell>

            <TableHeadCell class="w-[320px]">{t('table_actions')}</TableHeadCell>
        </TableHead>
        <TableBody id="ticket-table-body" tableBodyClass="divide-y" data-testid="ticket-table-body"> 
            {#if tickets.length === 0}
                <TableBodyRow id="no-tickets-row" data-testid="no-tickets-row">
                    <TableBodyCell colspan={10} class="text-center py-4 text-gray-500">
                        {t('table_no_ticket')}
                    </TableBodyCell>
                </TableBodyRow>
            {:else}
                {#each tickets as ticket}
                    <TableBodyRow
                        id="ticket-row-{ticket.id}"
                        data-testid="ticket-row"
                        class="cursor-pointer hover:bg-gray-50 {selectedTicketId === ticket.id ? 'bg-blue-50' : ''}"
                        on:click={() => handleRowClick(ticket)}
                    >
                        <TableBodyCell id="ticket-cell-id-{ticket.id}" data-testid="ticket-cell-id">
                            <div
                                id="ticket-link-{ticket.id}"
                                class="flex items-center justify-center py-2"
                                data-testid="ticket-link"
                            >
                                {ticket.id}
                            </div>
                        </TableBodyCell>
                        <TableBodyCell id="ticket-cell-status-{ticket.id}" data-testid="ticket-cell-status">
                            <div class="flex justify-center">
                                <span id="ticket-status-badge-{ticket.id}" class={`${getStatusClass(ticket.status.id)} px-2 py-1 rounded-md text-xs text-center`} data-testid="ticket-status-badge">
                                    <!-- {ticket.status.name.charAt(0).toUpperCase() + ticket.status.name.slice(1)} -->
                                    <!-- {ticket.status.name.charAt(0).toUpperCase() + ticket.status.name.slice(1).split('_').join(' ')} -->
                                     {t('tickets_' + ticket.status.name.toLowerCase())}
                                </span>
                            </div>
                        </TableBodyCell>
                        
                        <TableBodyCell id="ticket-cell-priority-{ticket.id}" data-testid="ticket-cell-priority">
                            <div class="flex justify-center">  
                                <span id="ticket-priority-badge-{ticket.id}" class={`${getPriorityClass(ticket.priority.name)} px-2 py-1 rounded-md text-xs text-center`} data-testid="ticket-priority-badge">
                                    {t('tickets_priority_' + ticket.priority.name.toLowerCase())}
                                </span>
                            </div>                        
                        </TableBodyCell>

                        <TableBodyCell id="ticket-cell-sentiment-{ticket.id}" data-testid="ticket-cell-sentiment">
                            <div class="flex justify-center"> 
                                <div id="ticket-sentiment-badge-{ticket.id}" class={`flex items-center justify-center gap-1 rounded-md p-1.5 ${getSentimentClass(ticket.sentiment)}`} data-testid="ticket-sentiment-badge">
                                    <img
                                        src={getSentimentIcon(ticket.sentiment)}
                                        alt={ticket.sentiment}
                                        class="w-5 h-5 object-cover"
                                    />
                                    <Tooltip>{ticket.sentiment ?? t('table_unclassified')}</Tooltip>
                                </div>
                            </div>
                        </TableBodyCell>

                        <TableBodyCell id="ticket-cell-customer-{ticket.id}" data-testid="ticket-cell-customer" class="overflow-hidden">
                            {#if ticket.customer.name}
                                <div id="ticket-customer-name-{ticket.id}" class="text-sm font-medium truncate" data-testid="ticket-customer-name">{ticket.customer.name}</div>
                                {#if ticket.customer.email}
                                    <div id="ticket-customer-email-{ticket.id}" class="text-xs text-gray-500 truncate" data-testid="ticket-customer-email">{ticket.customer.email}</div>
                                {/if}
                            {:else if ticket.customer.line_user && ticket.customer.line_user.display_name}
                                <div id="ticket-customer-display-name-{ticket.id}" class="text-sm font-medium truncate" data-testid="ticket-customer-display-name">{ticket.customer.line_user.display_name}</div>
                                {#if ticket.customer.line_user.user_id}
                                    <div id="ticket-customer-user-id-{ticket.id}" class="text-xs text-gray-500 truncate" data-testid="ticket-customer-user-id">ID: {ticket.customer.line_user.user_id}</div>
                                {/if}
                            {:else}
                                <div id="ticket-customer-unknown-{ticket.id}" class="text-sm truncate" data-testid="ticket-customer-unknown">{t('table_unknown')}</div>
                            {/if}
                        </TableBodyCell>

                        <TableBodyCell id="ticket-cell-contact-channel-{ticket.id}" data-testid="ticket-cell-contact-channel" class="overflow-hidden">
                            <div id="ticket-channel-name-{ticket.id}" class="text-sm truncate" data-testid="ticket-channel-name">{ticket.platform_identity.channel_name ? ticket.platform_identity.channel_name : '-'}</div>
                            <div id="ticket-platform-{ticket.id}" class="text-xs text-gray-500 truncate" data-testid="ticket-platform">
                                {ticket.platform_identity.platform ? ticket.platform_identity.platform : '-'}
                            </div>
                        </TableBodyCell>


                        <TableBodyCell id="ticket-cell-agent-{ticket.id}" data-testid="ticket-cell-agent" class="overflow-hidden">
                            <div id="ticket-agent-name-{ticket.id}" class="text-sm truncate" data-testid="ticket-agent-name">{ticket.owner.name ? ticket.owner.name : '-'}</div>
                            <div id="ticket-agent-role-{ticket.id}" class="text-xs text-gray-500 truncate" data-testid="ticket-agent-role">
                                {ticket.owner.role ? ticket.owner.role : '-'}
                            </div>
                        </TableBodyCell>
                        <TableBodyCell id="ticket-cell-created-on-{ticket.id}" data-testid="ticket-cell-created-on" class="overflow-hidden">
                            <span id="ticket-created-date-{ticket.id}" class="text-sm truncate block" data-testid="ticket-created-date">{displayDate(ticket.created_on).date}</span>
                            <span id="ticket-created-time-{ticket.id}" class="text-sm truncate block" data-testid="ticket-created-time">{displayDate(ticket.created_on).time}</span>
                        </TableBodyCell>
                        
                        <TableBodyCell id="ticket-cell-updated-ago-{ticket.id}" data-testid="ticket-cell-updated-ago" class="overflow-hidden">
                            <div id="ticket-updated-ago-{ticket.id}" class="text-sm truncate" data-testid="ticket-updated-ago">
                                <!-- <div>{ticket.updated_on}</div> -->
                                <!-- <div>{ticket.updated_ago}</div> -->
                                <!-- <div>{displayDate(ticket.updated_on).date}</div> -->
                                <!-- <div class="text-xs text-gray-500">{displayDate(ticket.updated_on).time}</div> -->
                                <div class="truncate">{timeAgo(ticket.updated_on, ticket.status.name)}</div>
                            </div>
                        </TableBodyCell>

                        <!-- TODO - Consistency on mouse over on disabled options -->
                        <TableBodyCell id="ticket-cell-actions-{ticket.id}" data-testid="ticket-cell-actions">
                            <!-- svelte-ignore a11y-click-events-have-key-events -->
                            <!-- svelte-ignore a11y-no-static-element-interactions -->
                            <div
                                id="ticket-action-container-{ticket.id}"
                                class="action-container"
                                data-testid="ticket-action-container"
                                on:click={handleActionClick}
                            >
                                <TransferTicketOwner 
                                    {ticket} 
                                    {users}
                                    loggedInUsername={loginUser.username}
                                    loggedInRole={loginUser.roles[0].name}
                                    onSuccess={handleTicketRefresh}
                                    isReadOnly={(ticket.status.name.toLowerCase() === 'pending_to_close') || (ticket.status.name.toLowerCase() === 'closed')}
                                />
                                <ChangeTicketPriority 
                                    {ticket} 
                                    {priorities}
                                    onSuccess={handleTicketRefresh}
                                    isReadOnly={(ticket.status.name.toLowerCase() === 'pending_to_close') || (ticket.status.name.toLowerCase() === 'closed')}
                                />
                                <ChangeTicketStatus
                                    {ticket}
                                    {statuses}
                                    {ticket_topics}
                                    {loginUser}
                                    onSuccess={handleTicketRefresh}
                                    isReadOnly={(ticket.status.name.toLowerCase() === 'pending_to_close') || (ticket.status.name.toLowerCase() === 'closed')}
                                />
                            </div>
                        </TableBodyCell>
                    </TableBodyRow>
                {/each}
            {/if}
        </TableBody>
    </Table>
</div>

<!-- Pagination Layout -->
<div id="ticket-table-pagination" data-testid="ticket-table-pagination" class="{isLoading ? 'pointer-events-none opacity-50' : ''}">
    <Pagination 
        {currentPage} 
        {totalPages} 
        visibleCount={10} 
        {updateCurrentPage}
        data-testid="pagination-container"
        pageButtonTestId="pagination-page"
        nextButtonTestId="pagination-next"
        prevButtonTestId="pagination-prev"
    />
</div>

<style>
    /* Add custom styles to ensure consistent appearance */
    :global(.tooltip) {
        z-index: 50;
    }
    
    /* Responsive action buttons */
    .action-container {
        display: grid;
        gap: 0.25rem;
    }
    
    /* Large screens - 3 columns */
    @media (min-width: 1024px) {
        .action-container {
            grid-template-columns: repeat(3, 1fr);
        }
    }
    
    /* Medium screens - 2 columns */
    @media (min-width: 768px) and (max-width: 1023px) {
        .action-container {
            grid-template-columns: repeat(2, 1fr);
        }
    }
    
    /* Small screens - 1 column */
    @media (max-width: 767px) {
        .action-container {
            grid-template-columns: 1fr;
        }
    }
    
    /* Ensure table doesn't break layout on small screens */
    @media (max-width: 1024px) {
        :global(table) {
            width: max-content !important;
        }
    }
</style>