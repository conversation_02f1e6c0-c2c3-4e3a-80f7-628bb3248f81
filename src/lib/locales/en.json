{"table_last_interaction": "Last Interaction", "login_username_label": "Username", "login_username_placeholder": "Enter your username", "login_username_validation_error": "Usernames may contain letters, numbers, dots (.), underscores (_), and hyphens (-) only.", "login_password_label": "Password", "login_password_placeholder": "Enter your password", "login_password_validation_error": "", "login_submit_button": "<PERSON><PERSON>", "login_submit_loading": "Logging in...", "login_submit_error_p1": "<PERSON><PERSON> failed", "login_submit_error_p2": "Please verify your username and password", "login_user_deactivated_p1": "Your account has been deactivated.", "login_user_deactivated_p2": "Please contact your supervisor.", "login_user_no_subscription_p1": "Your subscription is inactive.", "login_user_no_subscription_p2": "Please contact your administrator.", "time_ago.just_now": "Just now", "time_ago.minute": "minute", "time_ago.minutes": "minutes", "time_ago.hour": "hour", "time_ago.hours": "hours", "time_ago.day": "day", "time_ago.days": "days", "time_ago.week": "week", "time_ago.weeks": "weeks", "time_ago.month": "month", "time_ago.months": "months", "time_ago.year": "year", "time_ago.years": "years", "time_ago.ago": "ago", "day_monday": "Monday", "day_tuesday": "Tuesday", "day_wednesday": "Wednesday", "day_thursday": "Thursday", "day_friday": "Friday", "day_saturday": "Saturday", "day_sunday": "Sunday", "day_today": "Today", "day_yesterday": "Yesterday", "account": "Account", "your_account": "Your Account", "away": "Away", "business": "General", "customers": "Customers", "dashboard": "Dashboard", "home": "Home", "knowledgeBase": "Knowledge Base", "language": "Language", "logout": "Log Out", "online": "Online", "settings": "Settings", "status": "Status", "tasks": "My Tasks", "teamManagement": "Team Management", "subscription": "Subscription", "subscription_title": "Subscription", "subscription_description": "Manage your subscription plan, features, and usage.", "subscription_current_package": "Current Subscription", "subscription_tier": "Tier", "subscription_company": "Company", "subscription_serial_number": "Serial Number", "subscription_expires_on": "Expires On", "subscription_days_remaining": "Days Remaining", "subscription_expired": "Expired", "subscription_expired_title": "Subscription Expired", "subscription_expired_message": "Your subscription has expired. Please contact AIBL to restore service access.", "subscription_expiring_soon": "Expiring Soon", "subscription_expiry_warning": "Your subscription will expire soon. Renew now to avoid service interruption.", "subscription_not_found": "Subscription not found. Please contact AIBL.", "subscription_usage_stats": "<PERSON><PERSON><PERSON>", "subscription_item_active_users": "Active Users", "subscription_item_storage_limit": "Data Storage", "subscription_item_messages_per_minute": "Messages Per Minute", "subscription_item_line_accounts": "LINE Accounts", "subscription_item_ai_workflow_units": "AI Workflow Units", "subscription_item_unlimited": "Unlimited", "subscription_item_limit_exceeded": "Limit exceeded", "subscription_item_approaching_limit": "Approaching limit", "subscription_all_features": "Features", "subscription_feature_available": "Available", "subscription_feature_not_available": "Not Available", "userManual": "User Manual", "tickets": "Tickets", "testing": "Testing", "uploadFiles": "Upload Files", "users": "Users", "business_settings_title": "General", "business_settings_description": "Configure system preferences and manage application general settings.", "tab_system": "System", "tab_company": "Company", "tab_bot": "AI Chatbot", "tab_connection": "Connection", "appearance_title": "Website Appearance", "appearance_description": "Customize your website look and feel.", "upload_logo": "Upload Logo", "color_settings": "Color Settings", "modified": "modified", "saving": "Saving...", "save_logo": "Save Logo", "save_colors": "Save Colors", "dominant_color": "Dominant Color", "dominant_color_desc": "Used for main buttons, headers, and primary interactive elements.", "secondary_color": "Secondary Color", "secondary_color_desc": "Used for secondary buttons, backgrounds, and supporting elements.", "accent_color": "Accent Color", "accent_color_desc": "Used for highlights, notifications, and drawing attention to specific elements.", "recommended_size": "Recommended size", "unsaved_changes": "You have unsaved changes.", "timeout_settings": "Timeout Settings", "timeout_description": "Configure the system to automatically close tickets that remain unassigned after a period of inactivity.", "first_timeout": "First Timeout (minutes)", "first_timeout_desc": "Time before the first inactivity notification for an open ticket with no customer response.", "second_timeout": "Second Timeout (minutes)", "second_timeout_desc": "Time after the initial warning before closing an unassigned ticket with no customer response.", "logo_saved_success": "<PERSON><PERSON> saved successfully!", "click_and_drag": "Click to upload or drag and drop", "company_info_title": "Company Information", "company_info_description": "Manage your company details and business information.", "company_name_th": "Thai Company Name", "company_name_en": "English Company Name", "company_business": "Company Business", "business_type": "Business Type", "select_business_type": "Select business type", "save": "Save", "save_changes": "Save Changes", "changes_saved": "Changes saved successfully!", "chatbot_title": "Bot Configuration", "chatbot_description": "Customize your bot's personality and conversation style.", "chatbot_thai_name": "Thai Name", "chatbot_english_name": "English Name", "chatbot_role": "Role", "chatbot_gender": "Gender", "chatbot_style": "Conversation Style", "chatbot_type": "Conversation Type", "select_role": "Select Role", "select_gender": "Select Gender", "select_style": "Select Conversation Style", "select_type": "Select Conversation Type", "team_management": "Team Management", "team_management_description": "Configure tags, ticket transfer, business hours, and quick response templates.", "tab_user": "User", "tab_customer": "Customer", "tab_transfer": "Transfer", "partners": "Partners", "partners_description": "Manage your partners and collaborations.", "add_partner": "Add Partner", "enter_partner_name": "Enter Partner Name...", "enter_partner_code": "Enter Partner Abbreviation...", "update": "Update", "cancel": "Cancel", "confirm": "Confirm", "creating": "Creating...", "updating": "Updating...", "delete": "Delete", "next": "Next", "previous": "Previous", "select_color": "Select color", "departments": "Departments", "departments_desc": "Organize your content by departments or teams.", "add_department": "Add Department", "name": "Name", "name_desc": "Enter department name...", "code": "Abbreviation", "code_desc": "Enter department abbreviation...", "description": "Description", "description_desc": "Enter department description...", "specialized_tags": "Specialized Tags", "tag_members_by_role": "Tag members by role or expertise", "add_tag": "Add Tag", "enter_tag_name": "Enter tag name...", "tags_title": "Tags", "tags_description": "Create and manage tags to categorize customers.", "ticket_transfer_settings": "Ticket Transfer Settings", "ticket_transfer_description": "Configure how tickets are transferred and assigned.", "save_success": "Transfer settings saved successfully!", "unsaved_changes_warning": "You have unsaved changes to your transfer settings.", "step_1_title": "Role Assignment", "step_1_desc": "Assign tickets to agents based on their role.", "step_1_desc_remark": "This action will transfer the agent role only.", "step_2_title": "Department Matching", "step_2_desc": "Match tickets to agents in the appropriate department.", "enable_department": "Enable Department", "step_3_title": "Active Status Check", "step_3_desc": "Check if any agents are online and currently on shift.", "step_4_title": "Tag Matching", "step_4_desc": "Match tickets based on agent tags e.g, EV, PET, FIRE", "enable_tag": "Enable Tag", "step_5_title": "Additional Assignment Algorithms", "step_6_title": "Auto-Notification Settings", "step_6_desc": "Automatically notify customers when no admin responds within a specified time after ticket transfer.", "algorithm_frequent": "Frequently Assigned Agents", "algorithm_frequent_desc": "Agents who frequently handle similar tickets.", "algorithm_csat": "CSAT Score", "algorithm_csat_desc": "Agents with higher customer satisfaction ratings.", "algorithm_workload": "Workload Balancing", "algorithm_workload_desc": "Distribute tickets based on agent workload.", "default": "<PERSON><PERSON><PERSON>", "pagination_page": "Page", "account_settings": "Your Account", "account_settings_description": "Configure personal settings and details.", "tab_profile": "Profile", "tab_schedule": "Workshift", "tab_schedule_error_title": "Please fix the following errors:", "tab_schedule_error_start_time": "Start time must be before end time", "section_partners": "Partners", "section_departments": "Departments", "section_tags": "Tags", "your_info_title": "Your Information", "your_info_description": "Customize your information.", "first_name": "First name", "last_name": "Last name", "email": "Email", "phone_number": "Phone Number", "success_generic": "Your information has been updated successfully.", "error_generic": "Something went wrong. Please try again.", "work_shift_title": "Your workshift", "work_shift_column": "Workshift", "work_shift_description_1": "Individual work shift is used for assigning chats to agents, set up in automation hub, and does not affect login periods.", "work_shift_description_2": "Toggle off to configure user's work shift", "work_shift_update_success": "Work schedule updated successfully", "same_as_business_hours": "Same as business hours", "weekly_schedule": "Weekly Schedule", "off": "Off", "to": "to", "tickets_description": "Manage ticket assignments and statuses.", "view_my_tickets": "View My Tickets", "priority": "Priority", "sentiment": "Sentiment", "reset_filters": "Reset Filters", "search_placeholder": "Search by no., customer name, agent name, etc...", "tickets_total": "Total", "tickets_open": "Open", "tickets_change_to_open": "Open", "tickets_assigned": "Assigned", "tickets_change_to_assigned": "Assigned", "tickets_waiting": "Waiting", "tickets_change_to_waiting": "Waiting", "tickets_pending_to_close": "CSAT Pending", "tickets_pending_to_close_short": "Pending", "tickets_change_to_pending_to_close": "Pending to Close", "tickets_closed": "Closed", "tickets_change_to_closed": "Closed", "tickets_priority_low": "Low", "tickets_priority_medium": "Medium", "tickets_priority_high": "High", "tickets_priority_immediately": "Immediately", "table_no": "No.", "table_status": "Status", "table_priority": "Priority", "table_sentiment": "Sentiment", "table_customer": "Customer", "table_agent": "Agent", "table_time": "Created", "table_actions": "Actions", "table_updated_on": "Updated On", "table_updated_at": "Updated At", "table_updated_ago": "Last Updated", "table_no_ticket": "No ticket(s) to display", "table_unknown": "Unknown", "table_unclassified": "Unclassified", "transfer_ticket_title": "Transferring Ticket Ownership", "current_owner": "Current Owner:", "select_new_owner": "Select New Owner", "search": "Search", "warning": "Warning", "assign_offline_warning": "You are assigning this ticket to an offline user. They may not respond quickly.", "assign_away_warning": "You are assigning this ticket to a user who is away. Response may be delayed.", "transfer_ticket": "Transfer", "automated_transfer_ticket_warning": "This ticket will be automatically transferred to you.", "automated_transfer_ticket_success": "Ticket ownership automatically transferred to you.", "automated_transfer_ticket_failed": "Failed to automatically transfer ticket ownership.", "transfer_ticket_success": "Ticket ownership transferred successfully", "priority_modal_title": "Change Priority", "priority_modal_button": "Priority", "priority_select_label": "Select New Priority", "priority_modal_success": "Ticket priority changed successfully", "ticket_status": "Ticket Status", "ticket_status_success": "Ticket status changed successfully", "current_status": "Current Status", "select_new_ticket_status": "Select New Ticket Status", "additional_info_required": "Additional information required for closing this ticket:", "case_type": "Case Type", "case_topic": "Case Topic", "select_case_type": "Select Case Type", "case_topic_multiple": "Case Topic (Select multiple)", "please_select_case_topic": "Please select at least one case topic.", "please_change_status": "Please select a different status or cancel.", "users_page_title": "Accounts", "users_page_description": "Manage user roles and access.", "filter_status": "Status", "filter_role": "Role", "filter_partner": "Partner", "filter_department": "Department", "filter_specialized_tag": "Specialized Tag", "filter_reset": "Reset Filters", "search_user_placeholder": "Search by no. or name...", "table_workload": "Workload", "table_name": "Name", "table_role": "Role", "table_partner": "Partner", "table_specialize_tag": "Specialized Tag", "table_department": "Department", "table_last_active": "Last Active", "no_users": "No user(s) to display", "label": "Labels", "busy": "Busy", "offline": "Offline", "active": "Active", "inactive": "Inactive", "all_status": "All Status", "create_account": "Create Account", "creating_account": "Creating account...", "employee_id": "Account ID", "display_name": "Display name", "nickname": "Nickname", "username": "Username", "password": "Password", "confirm_password": "Confirm password", "change_password": "Change Password", "current_password": "Current password", "current_password_placeholder": "Enter current password", "new_password": "New password", "new_password_placeholder": "Enter new password", "confirm_new_password": "Confirm new password", "confirm_new_password_placeholder": "Confirm new password", "submit": "Submit", "password_change_success": "Password changed successfully", "profile_change_success": "Profile updated successfully", "new_account": "New account", "total_members": "Total users", "new_members_30_days": "New users (Last 30 days)", "active_members": "Online users", "labels": "Labels", "filter_tag": "Tag", "filter_platform": "Platform", "filter_all": "All", "filter_all_users": "All Users", "filter_active_only": "Active Only", "filter_inactive_only": "Inactive Only", "table_tag": "Tag", "table_phone": "Phone Number", "table_platform": "Platform", "table_email": "Email", "table_created_on": "Created", "table_no_data": "No customers to display", "customer_table_search_no_results": "No customers match your search or selected filters.", "customers_search_placeholder": "Search customers by, e.g., No., name, phone number, etc.", "no_partners": "No partners have been added yet.", "no_departments": "No departments have been added yet.", "no_tags": "No tags have been added yet.", "testing_page_title": "Chatbot Testing", "service_endpoints": "Service Endpoints", "faq_service": "FAQ Service", "example_message": "Example Message", "testing_section_title": "Testing", "testing_section_description": "Test chatbot responses using your knowledge base to ensure accurate and helpful answers.", "send": "Send", "clear": "Clear", "chatbot_response": "Chatbot Response", "reference_information": "Reference Information", "loading": "Loading...", "note": "Note", "note_faq": "All-Features combine Customer Support, Product Search and Promotion", "reference_question": "Question", "reference_answer": "Answer", "upload_documents": "Upload Documents", "documents": "Documents", "customer_support": "Customer Support", "promotion": "Promotion", "product": "Product", "trash": "Trash", "upload_files": "Upload files", "knowledge_base_description": "Organize documentation and helpful resources for your customer", "total": "Total", "business_hours": "Business Hours", "business_hours_description": "Set your chat's operating hours.", "business_hours_update_success": "Business hours updated successfully.", "business_hours_update_error": "Failed to update business hours.", "business_hours_out_of_hours_message_success": "Outside hours message saved successfully.", "business_hours_out_of_hours_message_failure": "Failed to save outside hours message.", "upload_title": "Upload Files", "select_category": "Select Category", "upload_file": "Upload File", "upload_image": "Select Image", "start_date": "Start Date", "end_date": "End Date", "description_upload_file": "Description (Optional)", "note_label": "Note", "note_admin_only": "Only Admin and Supervisor can upload documents", "note_upload_success": "Document was successfully uploaded!", "note_supported_format_customer": "Supported file formats: PDF, CSV, XLSX only", "note_supported_format_promotion": "Supported file formats: JPG, PNG, PDF only", "note_supported_format_product": "Supported file formats: XLSX, CSV only", "note_supported_image": "Images support only PNG, JPG", "note_message_placeholder": "Type your message here...", "note_product_type_label": "Select Product Type", "note_product_type_placeholder": "Select...", "upload_button": "Upload File", "uploading": "Uploading ...", "download_file": "Download File", "delete_permanently": "Delete Permanently", "permanently": "Permanently", "line_settings": "LINE", "webhook_settings": "Line Webhook Settings", "line_channel_secret": "LINE Channel Secret", "line_access_token": "LINE Access Token", "webhook": "Webhook", "copy_webhook": "<PERSON><PERSON>ok", "webhook_note": "The secret key from your LINE Messaging API settings.", "access_token_note": "The access token from your LINE Messaging API settings.", "line_qr_code": "LINE QR Code", "line_oa": "LINE Official Account", "line_group": "LINE Group", "gmail": "Gmail", "asterisk": "Asterisk", "link": "Link", "copy_link": "Copy link", "save_qr": "Save QR Code", "upload_qr_note": "Upload QR Code (SVG, PNG, JPG or GIF)", "upload_qr": "Upload QR Code", "whatsapp_settings": "WhatsA<PERSON> Settings", "disabled_input": "Disabled input", "facebook_messenger": "Facebook Messenger", "detail": "Detail", "user_profile": "User Profile", "view_users_profile_memberships": "View user’s profile memberships", "user_number": "User ID", "line_account": "LINE account", "no_line_account": "No LINE account", "no_connect_line": "LINE account doesn't connect yet...", "last_active": "Last active", "work_information": "Work Information", "view_users_work-related_memberships": "View user’s work-related memberships", "role": "Role", "partner": "Partner", "department": "Department", "specialize_tag": "Specialize Tag", "no_specialize_tags": "No Specialize tags", "current_workload": "Current workload", "my_tasks": "My Tasks", "customer_id": "Customer ID", "customer_name": "Customer name", "time": "Time", "updated_on": "Updated On", "no_tasks_assigned": "No tasks assigned", "policies": "Policies", "notes": "Notes", "memories": "Memories", "no_memory": "No memories have been added yet.", "export_customer_conversations": "Export All Conversations", "export_customer_conversations_success": "Conversations exported successfully", "export_customer_conversations_failed": "Failed to export conversations", "export_customer_notes": "Export All Notes", "no": "No.", "age": "Age", "social_platform": "Social Platform", "tag": "Tag", "address": "Address", "no_address": "No address", "edit_customer": "Edit Customer", "edit_customer_success": "Customer updated successfully.", "edit_tag": "Edit", "edit_tag_success": "Tag updated successfully.", "assign_customer_tags": "Assign <PERSON>s", "select_customer_tags": "Select customer tags", "overdue_ticket": "Overdue Ticket", "no_tickets_found": "No tickets found.", "total_policies": "Total Policies", "waiting_period": "Waiting Period", "nearly_expired": "Nearly Expired", "expired": "Expired", "policy_details": "Policy Details", "product_name": "Product Name", "product_type": "Product Type", "issue_date": "Issue Date", "see_more": "See more", "see_less": "See less", "policy_loading": "Loading policy details...", "policy_no_citizen_id": "No citizen ID provided.", "policy_not_available": "No policies available.", "policy_load_failed_p1": "Failed to load policy details.", "policy_load_failed_p2": "Please check the customer's citizen ID.", "policy_details_load_success": "Policy details loaded successfully.", "policy_details_load_failed": "Failed to load policy details.", "policy_tab": "Policies", "policy_no": "Policy No.", "policy_holder": "Policyholder", "policy_certificate_no": "Certificate No.", "policy_staff_no": "Staff No.", "policy_member_type": "Member Type", "policy_member_code": "Member Code", "policy_benefit_section": "Benefits", "policy_coverage_section": "Coverage", "policy_coverage_accident": "Accident", "policy_coverage_illness": "Illness", "policy_coverage_others": "Others", "policy_coverage_used": "Used", "policy_coverage_remarks": "Remarks", "policy_amount_unit_baht": "Baht", "policy_claims_empty": "No claims found for this policy", "policy_claims_diagnosis": "Diagnosis", "policy_claims_source": "Source", "policy_insurer_name": "Insurer", "policy_status_label": "Status", "policy_status_all": "All", "policy_status_active": "Active", "policy_status_pending": "Pending", "policy_status_nearly_expired": "Expiring in 30 days", "policy_status_expired": "Expired", "policy_filter_instructions": "Select status to filter policies", "policy_view_details_button": "View Details", "policy_details_tab": "Coverage", "search_policies_placeholder": "Search policies...", "policy_no_search_results": "No policies found matching your search criteria", "policy_search_suggestion": "Try adjusting your search terms or clearing filters", "policy_effective": "Effective", "policy_effective_from": "From", "policy_effective_to": "To", "policy_claims_tab": "Claims History", "policy_claims_no": "No.", "policy_claims_amount": "Charges", "policy_claims_amount_incurred": "Incurred", "policy_claims_amount_payable": "Payable", "policy_claims_date_visit": "Visit Date", "policy_claims_date_discharge": "Discharge Date", "policy_claims_date_settlement": "Payment Date", "policy_claims_provider": "Provider", "policy_claims_type": "Type", "policy_modal_close": "Close", "settings_policy_tab_name": "Policy & Claims Integration", "settings_policy_tab_title": "Policy & Claims Integration", "settings_policy_tab_description": "Workflow configurations and step-by-step process for fetching insurance policies and claims data", "settings_policy_workflow_loading": "Loading workflow configurations...", "settings_policy_workflow_loading_error": "Error Loading Workflows", "settings_policy_workflow_empty": "No policy workflow configurations are currently available.", "settings_policy_workflow_configuration_title": "Workflow Configuration", "settings_policy_workflow_configuration_api": "API Configuration", "settings_policy_workflow_configuration_execution": "Execution & Cache Settings", "settings_policy_workflow_configuration_dummy_data": "Dummy Values", "settings_policy_workflow_step_title": "Workflow Steps", "settings_policy_workflow_step_total": "Steps", "settings_policy_workflow_step_number": "Step", "settings_policy_workflow_step_end": "End of Workflow", "drop_file_here": "Drop file here", "type_a_message_placeholder": "Type a message", "ai_response": "AI Response", "connected": "Connected", "connecting": "Connecting...", "disconnected": "Disconnected", "transfer_ticket_ownership": "Transfer Ticket Ownership", "change_status": "Change Status", "information": "Information", "summary": "Summary", "ai_guidance": "AI Guidance", "customer_details": "Customer Details", "customer_details_description": "Access key information about the customer related to the current ticket.", "customer_notes": "Customer Notes", "customer_notes_description": "See the notes written about this customer.", "customer_sidebar_tab_overview": "Overview", "customer_sidebar_tab_overview_profile": "Profile", "customer_sidebar_tab_tickets": "Tickets", "customer_sidebar_tab_notes_memories": "Notes & Memories", "customer_sidebar_tab_consent_data": "Consent Data", "no_consent_data_found": "No consent data found for this customer.", "ticket_details": "Ticket Details", "ticket_details_description": "View and manage all relevant information associated with individual support tickets.", "previous_staffs": "Previous Staffs", "previous_staffs_description": "See the history of past staff members who have handled this ticket.", "customer_number": "Customer Number", "line_name": "Line Name", "not_specified": "Not specified", "optional": "Optional", "csat_settings": "Customer Satisfaction Survey Settings", "csat_description": "Upload and customize the image used for customer satisfaction surveys.", "upload_csat_image": "Upload CSAT Image", "save_csat_image": "Save CSAT Image", "all_conversations": "All Conversations", "select_conversation": "Select a conversation", "choose_platform_identity": "Choose a platform identity from the list to view messages", "search_platform": "Search", "select_conversation_view_details": "Select a conversation to view customer details", "chat_center": "Chat Center", "chat_center_all_messages": "All Messages", "chat_center_unread_messages": "Unread Messages", "chat_center_filter_daterange": "Date Range", "chat_center_filter_daterange_today": "Today", "chat_center_filter_daterange_yesterday": "Yesterday", "chat_center_filter_daterange_week": "This Week", "chat_center_filter_daterange_1month": "This Month", "chat_center_filter_daterange_3months": "Last 3 Months", "chat_center_filter_daterange_6months": "Last 6 Months", "chat_center_filter_status": "Status", "chat_center_filter_status_all": "All Statuses", "chat_center_filter_tag": "Tag", "chat_center_filter_tag_all": "All Tags", "chat_center_filter_owner": "Agent", "chat_center_filter_owner_all": "All Agents", "chat_center_filter_priority": "Priority", "chat_center_filter_priority_all": "All Priorities", "chat_center_filter_channel": "Channel", "chat_center_filter_channel_all": "All Channels", "chat_center_filter_button": "Filter", "chat_center_filter_empty": "No conversations found", "chat_center_clear_button": "Clear", "chat_center_filter_tab_my_assigned": "My Assigned", "chat_center_filter_tab_my_closed": "My Closed", "chat_center_filter_tab_open": "Open", "chat_center_filter_tab_all_assigned": "All Assigned", "chat_center_search_placeholder": "Search...", "chat_center_search_tooltip": "Search by agent, customer, or ticket", "chat_center_ticket_create": "New Conversation", "chat_center_ticket_creating": "Creating...", "chat_center_ticket_create_failed": "Failed to create new ticket", "chat_center_ticket_create_success": "New ticket created successfully", "chat_center_latest_message_is_template": "[LINE Object]", "chat_center_scroll_to_bottom": "Scroll to bottom", "chat_center_loading_messages": "Loading messages...", "chat_center_loading_more_messages": "Loading more messages...", "chat_center_load_more_conversations": "Load more conversations", "chat_center_loading_more_conversations": "Loading more conversations...", "chat_center_attachment_attach": "Attach File", "chat_center_attachment_label_count": "Files", "chat_center_attachment_clear_all": "Clear All", "chat_center_attachment_clearing_all": "Clearing all...", "chat_center_attachment_uploading": "Uploading...", "chat_center_attachment_upload_success": "Upload successful", "chat_center_attachment_remove_file": "Remove file", "chat_center_attachment_drop_here": "Drop files here to upload", "chat_center_attachment_file_count_exceeded": "Maximum MAX_FILES files allowed per message.", "chat_center_attachment_individual_file_size_exceeded": "Maximum individual file size must not exceed MAX_INDIVIDUAL_FILE_SIZE.", "chat_center_attachment_total_file_size_exceeded": "Maximum total file size must not exceed MAX_TOTAL_SIZE.", "chat_center_attachment_click_to_download": "Click to view or download", "chat_center_type_message": "Type your message here...", "chat_center_send_message": "Send Message", "chat_center_sending_message": "Sending message...", "timeline": "Timeline", "ai_assistant": "AI Assistant", "ask_ai": "Ask AI", "personalized_chatbot": "Personalized Chatbot", "restart_conversation": "Restart Conversation", "template_response": "Template Response", "new_template": "New Template", "quick_search": "Quick Search", "smart_reply": "Smart Reply", "search_type": "Search Type", "content_type": "Content Type", "quick_action": "Quick Actions", "copy": "Copy", "password_validation_msg_1": "Password must contain:", "password_validation_msg_2": "• At least 8 characters", "password_validation_msg_3": "• At least one lowercase and one uppercase letter", "password_validation_msg_4": "• At least one number", "password_validation_msg_5": "• At least one special character", "password_validation_msg_do_not_match": "Passwords do not match", "signup_form_required_badge": "Required", "signup_form_required_description": "All fields in this section are required", "signup_form_optional_badge": "Optional", "signup_form_optional_description": "Contact information for better communication", "signup_form_tab_personal_info": "Personal Info", "signup_form_tab_contact_details": "Contact Details", "signup_form_personal_phone": "Personal Phone", "signup_form_personal_email": "Personal Email", "signup_form_work_phone": "Work Phone", "signup_form_work_email": "Work Email", "signup_form_tab_preference": "Preferences", "signup_form_preferred_language": "Interface Language", "signup_form_preferred_language_placeholder": "Select Language", "signup_form_tab_emergency_contact": "Emergency Contact", "signup_form_emergency_name": "Emergency Contact Name", "signup_form_emergency_phone": "Emergency Contact Phone", "signup_form_emergency_email": "Emergency Contact Email", "signup_error_duplicated_username": "A user with that username already exists.", "signup_error_duplicated_email": "This email is already registered with another user.", "signup_error_invalid_email": "Please enter a valid email address.", "signup_error_username_length": "Username must be at least 3 characters long", "signup_error_username_start": "Username cannot start with dot, underscore, or hyphen", "signup_error_username_hyphen": "Username cannot contain multiple consecutive hyphens", "signup_error_username_dot": "Username cannot contain multiple consecutive dots", "signup_error_username_underscore": "Username cannot contain multiple consecutive underscores", "signup_error_username_characters": "Username can only contain letters, numbers, dots, underscores, and hyphens", "signup_error_username_restricted_words": "Username contains restricted words", "signup_error_phone_length": "Phone number must not exceed 20 characters", "signup_success_toast": "New account created successfully.", "signup_checking_quota": "Checking user creation quota...", "signup_quota_error": "User Creation Not Allowed", "signup_quota_available": "User Creation Available", "signup_remaining_slots": "remaining user slots", "signup_current_users": "Current users", "signup_error_quota_exceeded": "User creation quota has been exceeded", "signup_error_quota_check_failed": "Failed to check user creation quota", "user_edit_menu": "Edit", "user_edit_user": "Edit User", "user_assign_tag": "Assign <PERSON>s", "user_assign_tag_success": "Tag assigned successfully", "user_assign_partner": "Assign Partners", "user_assign_partner_success": "Partner assigned successfully", "user_assign_role": "Assign Roles", "user_assign_role_success": "Role assigned successfully", "user_assign_department": "Assign Departments", "user_assign_department_success": "Department assigned successfully", "user_assign_line_account": "Assign L<PERSON><PERSON> Account", "user_assign_workshift": "Assign Workshift", "user_deactivate_user": "Deactivate User", "user_deactivate_instructions_p1": "To confirm, please type", "user_deactivate_instructions_p2": "in the box below.", "user_deactivate_placeholder": "Enter username to confirm", "user_deactivate_validation_error": "Username does not match. Please type the exact username to confirm deactivation.", "user_deactivate_button": "Deactivate", "user_deactivate_success": "User deactivated successfully", "user_reactivate_user": "Reactivate User", "user_reactivate_instructions_p1": "To confirm, please type", "user_reactivate_instructions_p2": "in the box below.", "user_reactivate_placeholder": "Enter username to confirm", "user_reactivate_validation_error": "Username does not match. Please type the exact username to confirm reactivation.", "user_reactivate_button": "Reactivate", "user_reactivate_success": "User reactivated successfully", "user_reactivate_quota_error": "User Reactivation Not Allowed", "user_reactivate_quota_available": "User Reactivation Available", "user_sidebar_tab_overview": "Overview", "user_sidebar_tab_overview_profile": "Profile", "user_sidebar_tab_tickets": "Tickets", "user_sidebar_tab_security": "Account Security", "loading_notes": "Loading notes...", "staff_history": "Agent History", "customer_tags": "Tags", "basic_information": "Basic Information", "full_name": "Full Name", "customer_type": "Customer Type", "country": "Country", "company": "Company", "contact_channel": "Contact Channel", "not_provided": "Not provided", "no_notes_available": "No notes have been added yet.", "no_notes_found": "No notes found matching your search", "new_note": "Enter your note here...", "add_note": "Add Note", "search_note_placeholder": "Search notes...", "note_created_on": "Created on", "note_created_by": "by", "note_updated_on": "Updated on", "note_updated_by": "by", "note_edit": "Edit Note", "edit": "Edit", "sort": "Sort", "restore_to_default": "Restore to Default", "date_uploaded": "Date Uploaded", "all_filters": "All Filters", "filter": "Filter", "add_filter": "Add Filter", "after_start_date_upload": "After Start Date Upload", "before_end_date_upload": "Before End Date Upload", "after_promo_start_date": "After Promo Start Date", "before_promo_end_date": "Before Promo End Date", "selected": "selected", "filename": "Filename", "image_name": "Image Name", "no_documents": "There are no documents available for display at this time.", "current": "Current", "assigned_on": "Assigned On", "employee": "Agents", "loading_customer_summary": "Loading customer summary...", "error_loading_summary": "Error Loading Summary", "no_analysis_available": "No Analysis Available", "ticket_analysis_placeholder": "Ticket analysis summaries will appear here", "ticket_summary": "Ticket Summary", "all_events": "All Events", "transfers": "Transfers", "closures": "Closures", "open": "Open", "try_again": "Try Again", "address_line1": "Address Line 1", "address_line2": "Address Line 2", "city": "City", "state_province_region": "State / Province / Region", "zip_code": "ZIP / Postal Code", "date_of_birth": "Date of Birth", "personal_information": "Personal Information", "address_information": "Address Information", "chatbot_workflow": "Chatbot Workflow", "chatbot_workflow_description": "This section will guide you through how the chatbot processes and handles customer interactions.", "underprogress": "This feature is under development.", "close-ticket": "Close Ticket", "transfer-ticket": "Transfer Ticket", "translation": "Translation", "language_name_en": "English", "language_name_th": "Thai", "chat_integrations": "Chat Integrations", "chat_integrations_description": "Number of connected accounts included in your plan:", "discover": "Discover", "line_official_account": "Line Official Account", "line_quota_exceeded": "LINE Quota Exceeded", "connect_line_description": "Connect at least 1 account for each", "facebook_instagram": "Facebook / Instagram", "whatsapp_business": "WhatsApp Business", "shopee": "<PERSON>ee", "lazada": "<PERSON><PERSON><PERSON>", "tiktok_shop": "TikTok Shop", "connect_multiple_description": "Connect multiple accounts", "connect": "Connect", "not_available_yet": "Not available yet", "no_owner_history_available": "No owner history available.", "search_filename": "Search Filename", "pending_to_close": "Pending to Close", "closed": "Closed", "connect_line_business": "Connect LINE Business", "line_business_configuration_description": "Enter your LINE Business configuration details to establish the connection.", "connection_name": "Channel Name", "connection_name_placeholder": "My LINE Business Account", "connection_name_description": "A friendly name for this LINE connection", "channel_id": "Channel ID", "channel_id_placeholder": "**********", "channel_id_description": "Your LINE Channel ID from LINE Developers Console", "channel_secret": "Channel Secret", "channel_secret_placeholder": "••••••••••••••••", "channel_secret_description": "Your LINE Channel Secret", "channel_access_token": "Channel Access Token", "channel_access_token_placeholder": "••••••••••••••••", "channel_access_token_description": "Your LINE Channel Access Token", "line_provider_id": "LINE Provider ID", "line_provider_id_placeholder": "provider-123", "line_provider_id_description": "Your LINE Provider ID", "line_provider_name": "LINE Provider Name", "line_provider_name_placeholder": "My Business Provider", "line_provider_name_description": "Your LINE Provider Name", "messaging_api_monthly_quota_usage": "Usage", "messaging_api_messages_used": "Used", "messaging_api_of": "of", "messaging_api_messages_unit": "messages", "messaging_api_usage_percentage": "Usage", "verification_status": "Verification Status", "not_verified": "Not Verified", "verified": "Verified", "verification_status_description": "Select whether this connection has been verified", "webhook_settings_description": "*Click copy to use this link in Webhook settings (URL)", "connection_saved_successfully": "Connection saved successfully!", "webhook_url_copied": "Webhook URL copied to clipboard!", "verify": "Verify", "manage_line_connection": "Manage Line Connection", "connection_status": "Connection Status", "connection_active": "Active", "connection_disabled": "Disabled", "test_connection": "Test Connection", "manage": "Manage", "webhook_url": "Webhook URL", "there_are": "There are", "accounts_has_been_connected": "have been connected", "enable_auto_notification": "Enable Auto-Notification", "response_time_limit": "Response Time Limit", "response_time_limit_description": "If no admin responds within this time, the notification will be sent", "auto_notification_message": "Auto-Notification Message", "auto_notification_message_placeholder": "Enter the message to be sent when auto-notification is triggered...", "auto_notification_message_description": "This message will be sent to notify when no response is received within the time limit", "chatbot_behavior_outside_business_hours": "Chatbot Behavior Outside Business Hours", "configure_automatic_message_outside_hours": "Configure the automatic message sent to customers when they contact outside business hours when the ticket is transferred to a staff member during that time.", "automatic_message_outside_hours_description": "This message will be automatically sent to customers who contact stafffs outside your business hours.", "visit_link": "Visit Link", "click_to_visit_link": "Click to visit link", "qr_codes_links_description": "Upload QR codes and add links for LINE Official Account and LINE Group to complete the connection setup.", "qr_code": "QR Code", "line_oa_link": "LINE OA Link", "line_group_link": "LINE Group Link", "qr_codes_and_links": "QR Codes & Links", "connection_details": "Connection Details", "line_oa_link_placeholder": "Enter LINE Official Account link", "line_group_link_placeholder": "Enter LINE Group link", "type_message": "Type a message...", "csat_rating_system": "Customer Satisfaction Rating System", "csat_rating_description": "Customers can choose from 5 satisfaction levels as follows:", "excellent": "Excellent", "good": "Good", "ok": "OK", "poor": "Poor", "need_improvement": "Need Improvement", "five_points": "5 Points", "four_points": "4 Points", "three_points": "3 Points", "two_points": "2 Points", "one_point": "1 Point", "csat_calculation_note": "The system will calculate the average satisfaction score from all customer ratings", "upload_logo_note": "The system will display the logo on the sidebar.", "line_group_connection_description": "Save your QR code and link for LINE Group connection.", "setup_instructions": "Setup Instructions", "create_line_group": "Create LINE Group", "invite_chatbot": "Invite chatbot to LINE Group", "invite_employees": "Invite employees to LINE Group", "employees_connect_account": "Employees connect their LINE account in settings", "important_note": "Important", "unconnected_warning": "If employees haven't connected their LINE accounts, the chatbot will not be able to notify them in the LINE group", "saving_settings": "Saving settings...", "settings_saved_successfully": "Setting<PERSON> saved successfully!", "error": "Error", "save_failed": "Unable to save", "download": "Download", "message_template_empty_or_invalid": "Message template is empty or invalid.", "no_messages_yet": "No messages yet. Start a conversation!", "no_tags_available": "No tags available. Please create tags in settings first.", "partner_name_placeholder": "Enter Partner Name...", "partner_abbreviation_placeholder": "Enter Partner Abbreviation...", "contact_identity_information": "Contact Information", "gender": "Gender", "middle_name": "Middle Name", "male": "Male", "female": "Female", "other": "Other", "nationality": "Nationality", "select_nationality": "Select Nationality", "national_id": "Thai Citizen ID", "passport_number": "Passport Number", "career": "Career", "preferred_language": "Preferred Language", "preferred_contact_method": "Preferred Contact Method", "select_country": "Select Country", "select_language": "Select Language", "select_contact_method": "Select Contact Method", "customer_updated_successfully": "Customer updated successfully", "failed_to_update_customer": "Failed to update customer", "network_error_occurred": "Network error occurred", "loading_business_hours": "Loading business hours...", "no_times_set": "No times set", "add_slot_time": "Add time slot", "slot": "Slot", "and": "and", "time_overlap": "Time ranges cannot overlap", "quick_response_templates": "Quick Response Templates", "search_templates": "Search templates...", "add_template": "Add Template", "template_keyword": "Keyword", "template_keyword_placeholder": "Enter keyword (e.g., thanks, welcome)", "template_description": "Description", "template_description_placeholder": "Brief description of this template", "template_content": "Template Content", "template_content_placeholder": "Enter template message...", "template_keyword_required": "Keyword is required", "template_keyword_min_length": "Keyword must be at least 2 characters", "template_keyword_max_length": "Keyword must be less than 20 characters", "template_keyword_duplicate": "This keyword already exists", "template_content_required": "Template content is required", "template_content_min_length": "Template content must be at least 10 characters", "template_content_max_length": "Template content must be less than 500 characters", "template_description_required": "Description is required", "template_description_max_length": "Description must be less than 100 characters", "template_add_error": "Failed to add template", "template_edit_error": "Failed to update template", "template_delete_error": "Failed to delete template", "confirm_delete": "Are you sure you want to delete?", "loading_templates": "Loading templates...", "no_templates_found": "No templates found matching your search", "no_templates_yet": "No templates created yet. Add your first template to get started.", "adding": "Adding...", "deleting": "Deleting...", "preview": "Preview", "template_preview": "Template Preview", "autocomplete_preview": "Autocomplete Preview", "how_it_appears_in_autocomplete": "How it appears in autocomplete:", "close": "Close", "responses_template": "Quick Responses", "is_active": "Account Status", "enabled": "Active", "suspended": "Deactivated", "analytics_dashboard": "Analytics Dashboard", "team_performance": "Team Performance", "work_quality": "Work Quality", "daily_summary": "Daily Summary", "work_quality_metrics": "Work Quality Metrics", "select_dashboard": "Select Dashboard", "loading_dashboard": "Loading dashboard...", "no_dashboard_available": "No Dashboard Available", "select_dashboard_message": "Please select a dashboard to view work quality metrics.", "team_performance_coming_soon": "Team performance metrics and analytics will be displayed here. This section is coming soon!", "daily_summary_coming_soon": "Daily performance summary and key insights will be displayed here. This section is coming soon!", "failed_to_load_dashboard": "Failed to load dashboard", "team_performance_metrics": "Team Performance Metrics", "knowledge_base": "Knowledge Base", "user_details": "User Details", "no_messages": "No messages", "image_modal_close_hint": "Click anywhere to close • Press ESC to close", "no_matching_tags": "No matching tags", "customer_search_placeholder": "Search...", "customer_search_field_label": "Search by", "customer_search_field_id": "No.", "customer_search_field_name": "Name", "customer_search_field_email": "Email", "customer_search_field_phone": "Phone Number", "customer_search_field_platform": "Platforms", "customer_search_field_tag": "Tags", "customer_search_field_note": "Notes", "input_error_not_ticket_owner": "You are not the current owner of this ticket and cannot send messages.", "input_error_ticket_pending_to_close": "This ticket is pending for customer satisfaction score and you cannot send messages.", "input_error_ticket_closed": "This ticket is closed and you cannot send messages.", "input_error_create_new_ticket": "Press 'New Conversation' button to send a new message.", "input_error_message_contains_invalid_content": "Message contains invalid content.", "cannot_send_ticket_message": "You cannot send messages for this ticket.", "no_schedule_set": "No schedule set", "no_users_to_display": "No user(s) to display", "admin": "Admin", "agent": "Agent", "customer": "Customer", "supervisor": "Supervisor", "system": "System", "validate_phone_number": "Must be at least 10 digits", "validate_phone_number_success": "Phone number is valid", "validate_email": "Must contain '@' and valid format", "validate_email_success": "Email is valid", "validate_national_id": "Must be exactly 13 digits and numbers only", "validate_national_id_success": "Thai Citizen ID is valid", "validate_passport_number": "Must be 7-9 characters (letters and numbers)", "validate_passport_number_success": "Passport number is valid", "placeholder_message": "No validation required", "date_of_birth_placeholder": "dd/mm/yyyy", "date_of_birth_system_note": "System stores data in Christian Era (CE).", "customer_sentiment": "Customer Sentiment", "customer_topic_breakdown": "Customer Topic Breakdown", "usefulness_of_ai": "Usefulness of AI", "agent_performance": "Agent Performance", "chat_performance": "Chat Performance", "response_time_volume": "Response Time & Volume", "human_agent_performance_all": "Human Agent Performance Dashboard (All)", "human_agent_performance_single": "Human Agent Performance Dashboard (Single)", "unresolved_tickets_dashboard": "Unresolved Tickets Dashboard", "agent_online_offline_statistics": "Agent Online/Offline Statistics", "at_least_one_day_required": "Please select at least one day for your work schedule", "reprint": "Reprint", "renewal_notice": "Renewal Notice", "endorsement": "Endorsement", "quotation": "Quotation", "complaint": "<PERSON><PERSON><PERSON><PERSON>", "technical_issue": "Technical Issue", "insurance_products": "Insurance Products", "policy_information_inquiry": "Policy Information Inquiry", "report_insurance_claims": "Report Insurance Claims", "application_process": "Application Process", "claiming_compensation": "Claiming Compensation", "check_list_of_representatives_agents": "Check List of Representatives/Agents", "insurance_payment_channels": "Insurance Payment Channels", "service_network": "Service Network", "contact_branches": "Contact/Branches", "follow_up_on_previous_reports": "Follow Up on Previous Reports", "others": "Others", "non_motor": "Non-Motor", "motor": "Motor", "agent_brokers": "Agent/Brokers", "name_of_the_insured": "Name of the Insured", "name_of_the_beneficiary": "Name of the Beneficiary", "change_address_in_policy": "Change Address in Policy", "national_id_number": "National ID Number", "act": "Act", "staff": "Staff", "service": "Service", "website": "Website", "mobile_app": "Mobile App", "api": "API", "ticket": "Ticket", "liff_template": "LIFF Templates", "liff_template_description": "Templates for creating new LIFF apps.", "add_liff_app": "Add LIFF App", "liff_applications": "LIFF Applications", "line_liff_apps_description": "Manage the LIFF applications for this LINE connection.", "liff_app_name": "LIFF App Name", "liff_id": "LIFF ID", "liff_url": "LIFF URL", "life_endpoint": "LIFF Endpoint", "actions": "Actions", "no_liff_apps_found": "No LIFF apps found.", "add_first_liff_app": "Add your first LIFF app to get started.", "edit_liff_app": "Edit LIFF App", "confirm_delete_liff_app": "Are you sure you want to delete this LIFF app?", "liff_app_added_successfully": "LIFF app added successfully.", "add_liff_app_failed": "Failed to add LIFF app.", "liff_app_updated_successfully": "LIFF app updated successfully.", "update_liff_app_failed": "Failed to update LIFF app.", "liff_app_deleted_successfully": "LIFF app deleted successfully.", "delete_liff_app_failed": "Failed to delete LIFF app.", "liff_app_name_placeholder": "e.g., My LIFF App", "liff_apps": "LIFF Apps", "line_channel": "LINE Channel", "line_login_channel_id": "LINE Login Channel ID", "line_login_channel_name": "LINE Login Channel Name", "line_login_channel_secret": "LINE Login Channel Secret", "pdpa_consent": "PDPA Consent", "more_template": "More Templates", "create_new_template": "Create New Template", "coming_soon": "Coming Soon", "already_exists": "Already Exists", "selectMenuLeft": "Please select a menu from the left.", "by": "By", "tokens": "Tokens", "cost": "Cost", "load_more": "Load More", "db.title": "Dashboard", "db.header": "Dashboard", "db.teamPerformance": "Team Performance", "db.workQuality": "Work Quality", "db.agentPerformance": "Agent Performance", "db.chatPerformance": "Chat Performance", "db.sla": "Service Level Agreement (SLA)", "db.responseTimeVolume": "Response Time & Volume", "db.workQualityDetail": "Work Quality", "db.customerSatisfaction": "Customer Satisfaction", "db.timeRange": "Time Range", "db.Today": "Today", "db.Yesterday": "Yesterday", "db.last7Days": "Last 7 Days", "db.last30Days": "Last 30 Days", "db.thisMonth": "This Month", "db.lastMonth": "Last Month", "db.custom": "Custom", "db.downloadCsvFullPage": "Download Full Page CSV", "db.downloadExcel": "Download Excel File", "db.clearFilters": "Clear Filters", "db.expand": "Expand", "db.noDataAvailable": "No Data Available", "db.timeUnitDay": "d", "db.timeUnitHour": "h", "db.timeUnitMinute": "m", "db.excelAlert": "Failed to download the file. Please try again later.", "db.loadingData": "Loading Data", "downloadFavorites.buttonText": "Download Favorites", "downloadFavorites.downloading": "Downloading...", "downloadFavorites.noFavorites": "No favorite dashboards", "downloadFavorites.confirmDownload": "This action will download {count} dashboards with data from {startDate} to {endDate}. Continue?", "downloadFavorites.downloadError": "Failed to download favorite dashboards. Please try again.", "error.missingAccessToken": "Access token is required", "dbAgent.agent": "Agent", "dbAgent.allAgents": "All Agents", "dbAgent.individualPerformance": "Individual Performance", "dbAgent.individualPerformanceExcel": "Individual_Performance", "dbAgent.avgResponseTimeSeconds": "Average Response Time (seconds)", "dbAgent.avgHandlingTimeMinutes": "Average Handling Time (minutes)", "dbAgent.avgCsatScoreOutOf5": "Average CSAT (out of 5)", "dbAgent.ticketsTransferred": "Tickets Transferred to Others", "dbAgent.ticketsTransferredExcel": "Tickets_Transferred_To_Others", "dbAgent.amountTransferred": "Amount Transferred", "dbAgent.percentageComparedToPreviousPeriod": "% Compared to Previous Period", "dbAgent.ticketsReceived": "Tickets Received From Others", "dbAgent.ticketsReceivedExcel": "Tickets_Received_From_Others", "dbAgent.amountReceived": "Amount Received", "dbAgent.responseRate5Min": "Response Rate in 5 mins (%)", "dbAgent.responseRate5MinExcel": "Response_Rate_in_5_mins", "dbAgent.percentageResponse5Min": "% Response in 5 mins", "dbAgent.agentOverallPerformanceSummary": "Agent Overall Performance Summary", "dbAgent.agentOverallPerformanceSummaryExcel": "Agent_Overall_Performance_Summary", "dbAgent.amountOfClosedTickets": "Amount of Closed Tickets", "dbAgent.amountOfUnclosedTickets": "Amount of Unclosed Tickets", "dbAgent.avgResponseTime": "Average Response Time (seconds)", "dbAgent.avgHandlingTime": "Average Handling Time (minutes)", "dbAgent.avgCsat": "Average CSAT (out of 5)", "dbAgent.unclosedTicketsOver1Day": "Unclosed Tickets: Over 1 Day Old", "dbAgent.unclosedTicketsOver1DayExcel": "Unclosed_Tickets_Over_1_Day", "dbAgent.closedTicketsOver1Day": "Closed Tickets: Over 1 Day Old", "dbAgent.closedTicketsOver1DayExcel": "Closed_Tickets_Over_1_Day", "dbResponseTimeVolume.totalIncomingMessages": "Incoming Messages (From Line)", "dbResponseTimeVolume.totalTickets": "Ticket Counts (From Line)", "dbResponseTimeVolume.dailyIncomingChatVolume": "Daily Total Incoming Message Volume", "dbResponseTimeVolume.dailyIncomingChatVolumeExcel": "Daily_Total_Incoming_Message_Volume", "dbResponseTimeVolume.totalTicketsByCategory": "Total Tickets by Category", "dbResponseTimeVolume.incomingMessagesByTimeSlot": "Total Incoming Messages by Time Slot", "dbResponseTimeVolume.incomingMessagesByTimeSlotExcel": "Total_Incoming_Messages_By_Time_Slot", "dbResponseTimeVolume.timeSlot": "Time Slot", "dbResponseTimeVolume.monday": "Mon", "dbResponseTimeVolume.tuesday": "<PERSON><PERSON>", "dbResponseTimeVolume.wednesday": "Wed", "dbResponseTimeVolume.thursday": "<PERSON>hu", "dbResponseTimeVolume.friday": "<PERSON><PERSON>", "dbResponseTimeVolume.saturday": "Sat", "dbResponseTimeVolume.sunday": "Sun", "dbWorkQuality.averageCsatScoreOutOf5": "Average CSAT Score (out of 5)", "dbWorkQuality.averageFirstResponseTimeSeconds": "Average First Response Time (seconds)", "dbWorkQuality.averageResponseTimeSecondsAgentVsChatbot": "Average Response Time (seconds): Agent vs. <PERSON><PERSON><PERSON>", "dbWorkQuality.averageResponseTimeSecondsAgentVsChatbotExcel": "Average_Response_Time_Agent_vs_Cha<PERSON>bot", "dbWorkQuality.averageCsatScoreOutOf5Daily": "Average CSAT Score (out of 5) Daily", "dbWorkQuality.averageCsatScoreOutOf5DailyExcel": "Daily_Average_CSAT_Score", "dbWorkQuality.averageFirstResponseTimeSecondsDaily": "Average First Response Time (seconds) Daily", "dbWorkQuality.averageFirstResponseTimeSecondsDailyExcel": "Daily_Average_First_Response_Time", "dbWorkQuality.averageResponseTimeSecondsDaily": "Average Response Time (seconds) Daily", "dbWorkQuality.averageResponseTimeSecondsDailyExcel": "Daily_Average_Response_Time", "dbWorkQuality.totalSentimentCount": "Total Sentiment Count", "dbWorkQuality.totalSentimentCountExcel": "Total_Sentiment_Count", "dbWorkQuality.dailySentimentCount": "Daily Sentiment Count", "dbWorkQuality.dailySentimentCountExcel": "Daily_Sentiment_Count", "dbWorkQuality.sentimentCountClosedTicketsByCaseType": "Sentiment Count of Closed Tickets by Case Type", "dbWorkQuality.sentimentCountClosedTicketsByCaseTypeExcel": "Sentiment_Count_of_Closed_Tickets_By_Case_Type", "dbWorkQuality.averageCsat": "Average CSAT", "dbWorkQuality.timeSeconds": "Time (seconds)", "dbWorkQuality.ticketCount": "Ticket Count", "dbChatPerformance.totalIncomingMessages": "Total Incoming Messages", "dbChatPerformance.totalAgentTickets": "Total Agent Tickets", "dbChatPerformance.totalAgentTicketsTooltip": "This also includes closed tickets by the agent.", "dbChatPerformance.totalAgentClosedTickets": "Total Agent Closed Tickets", "dbChatPerformance.agentTicketClosureRateVsIncoming": "Agent Ticket Closure Rate vs Incoming (%)", "dbChatPerformance.averageAgentResponseTimeSeconds": "Average Agent Response Time (seconds)", "dbChatPerformance.averageAgentResponseTimeMinutes": "Average Agent Response Time (minutes)", "dbChatPerformance.averageAgentResponseTimeHours": "Average Agent Response Time (hours)", "dbChatPerformance.agentResponseRateWithin6Seconds": "Agent Response Rate Within 6 Seconds (%)", "dbChatPerformance.averageAgentHandlingTimeSeconds": "Average Agent Handling Time (seconds)", "dbChatPerformance.averageAgentHandlingTimeMinutes": "Average Agent Handling Time (minutes)", "dbChatPerformance.averageAgentHandlingTimeHours": "Average Agent Handling Time (hours)", "dbChatPerformance.agentHandlingRateWithin5Minutes": "Agent Handling Rate Within 5 Minutes (%)", "dbChatPerformance.agentResponseRateWithin": "Agent Response Rate Within", "dbChatPerformance.agentHandlingRateWithin": "Agent Handling Rate Within", "dbChatPerformance.agentTicketsByStatus": "Agent Tickets By Status", "dbChatPerformance.agentTicketsByStatusExcel": "Agent_Tickets_By_Status", "dbChatPerformance.ticketNo": "Ticket No", "dbChatPerformance.status": "Status", "dbChatPerformance.customer": "Customer", "dbChatPerformance.priority": "Priority", "dbChatPerformance.sentiment": "Sentiment", "dbChatPerformance.agent": "Agent", "dbChatPerformance.createdTime": "Created Time", "dbChatPerformance.currentTime": "Current Time", "dbChatPerformance.totalUsedTime": "Total Used Time", "dbChatPerformance.closedTime": "Closed Time", "dbChatPerformance.agentClosedTicketsByCaseType": "Closed Tickets by Agent: Case Type", "dbChatPerformance.agentClosedTicketsByCaseTypeExcel": "Case_Type_Of_Closed_Tickets_By_Agent", "dbChatPerformance.count": "Amount", "dbChatPerformance.agentClosedTicketsBySubCaseType": "Closed Tickets by Agent: Sub-Case Type", "dbChatPerformance.agentClosedTicketsBySubCaseTypeExcel": "Sub_Case_Type_Of_Closed_Tickets_By_Agent", "dbChatPerformance.agentClosedTicketsCaseAndSubCase": "Closed Tickets by Agent: Case & Sub-Case Type", "dbChatPerformance.agentClosedTicketsCaseAndSubCaseExcel": "Case_Type_And_Sub_Case_Type_Of_Closed_Tickets_By_Agent", "dbChatPerformance.caseType": "Case Type", "dbChatPerformance.subCaseType": "Sub-Case Type", "dbScoreCard.comparedToPreviousPeriod": "Compared to previous period", "dbCSAT.csatScore": "CSAT Score", "dbCSAT.lowCSATbyTicket": "Ticket Satisfaction: 1", "dbCSAT.lowCSATbyTicketExcel": "Ticket_Satisfaction_1", "dbAgent.closedTicketsWithCSATExcel": "Closed_Tickets_With_CSAT_By_Agent", "load_more_older_tickets": "Load More Older Tickets", "load_more_newer_tickets": "Load More Newer Tickets", "choose_ticket_to_view": "Choose a ticket to view details", "select_ticket_view_details": "Select a ticket to view details", "select_ticket": "Select Ticket", "back_to_ticket_page": "Back to Tickets", "consent_status_accepted": "ACCEPTED", "consent_status_declined": "DECLINED", "consent_status_not_provided": "NOT PROVIDED", "consent_purposes_basic_processing": "Basic Processing", "consent_purposes_analytics": "Analytics", "consent_purposes_security": "Security", "consent_purposes_marketing": "Marketing", "consent_allowed": "Allowed", "consent_not_allowed": "Not Allowed", "consent_version": "Version", "consent_language": "Language", "consent_acceptance_date": "Consent Granted On", "consent_expires_date": "<PERSON>id <PERSON>", "consent_platform": "Platform", "consent_purposes": "Purposes", "consent_consent_history_all": "All Consent History", "consent_record_item": "Record", "consent_id": "ID", "consent_date": "Date", "consent_expires": "Expires", "consent_no_consent_history": "No consent history found", "consent_no_consent_data_found": "No consent data found", "select_start_date": "Select Start Date", "select_end_date": "Select End Date", "remaining_quota": "<PERSON><PERSON><PERSON>", "loading_quota": "Loading Quota...", "records": "Records", "record": "Record", "user.deactivated_badge": "Account Deactivated", "back": "Back", "settings.team.ticketTopic.title": "Ticket Case Topics", "settings.team.ticketTopic.description": "Manage case types and topics for ticket categorization", "settings.team.ticketTopic.addNewTopic": "Add Case Topic", "settings.team.ticketTopic.editTopic": "Edit Case Topic", "settings.team.ticketTopic.deactivateTopic": "Deactivate Case Topic", "settings.team.ticketTopic.reactivateTopic": "Reactivate Case Topic", "settings.team.ticketTopic.createOther": "Other (create new)", "create": "Create", "settings.team.ticketTopic.editWarning": "Past tickets that use this case topic will have their case case topic modified accordingly. This will affect some information in the dashboard and chat summary.", "settings.team.ticketTopic.updateTopic": "Update Case Topic", "settings.team.ticketTopic.deactivateTopicDescription": "Are you sure you want to deactivate this case topic? This will make it unavailable for new tickets but preserve historical data.", "settings.team.ticketTopic.reactivateTopicDescription": "Are you sure you want to reactivate this case topic? This will make it available for new tickets again.", "settings.team.ticketTopic.deactivateTopicWarning": "The case topic will be preserved for dashboard, past tickets, and chat center summary but won't be available for new tickets.", "settings.team.ticketTopic.reactivateTopicWarning": "Reactivating this case topic will make it available for selection when closing new tickets.", "deactivation": "Deactivating...", "reactivation": "Reactivating...", "settings.team.ticketTopic.existingTopicFound": "Similar Case Type and Topic Found", "settings.team.ticketTopic.existingTopicFoundDescription": "A case topic with the same name and case type already exists.", "settings.team.ticketTopic.existingTopic": "Similar Case Type and Topic", "settings.team.ticketTopic.created": "Created at", "settings.team.ticketTopic.updated": "Updated at", "settings.team.ticketTopic.yourUpdatedTopic": "Your Updated Case Type and Topic", "settings.team.ticketTopic.yourNewTopic": "Your New Case Type and Topic", "no_description": "No description", "proceed_anyway": "Proceed Anyway", "processing": "Processing...", "settings.team.ticketTopic.duplicateModalSuggestion": ["To resolve this, you can:", "Use the existing case topic", "Reactivate and use the existing case topic", "Choose a different name for the updated case topic", "Choose a different name for the new case topic", "Rename the existing case topic", "This will create a new case topic as requested. Note that the new case topic will be considered as a different case topic from the existing one."], "settings.team.ticketTopic.placeholder.caseType": "Enter case type (required)", "settings.team.ticketTopic.placeholder.caseTopic": "Enter case topic (required)", "settings.team.ticketTopic.placeholder.description": "Enter description (optional)", "metric_performance": "Metric Performance", "configure_sla": "Configure SLA", "indicators_name": "Indicators Name", "descriptions": "Descriptions", "actual": "Actual", "target": "Target", "sla_title": "SLA Configurations", "sla_description": "Configure the target values, reporting time, and recipients for each performance indicator", "downloadFavorites_title": "Download Favorites", "downloadFavorites_description": "Download each of favorited dashboards in Excel (.xlsx) format", "upload_files_description": "Upload documents for the AI to learn and answer questions from this information.", "filter_category": "Category", "filter_access_level": "Access Level", "all_documents": "All documents", "file_type": "File Type", "api_integration": "API Integration", "api_description": "Add APIs and tools to the AI Assistant to connect with your company's data.", "search_apis": "Search APIs", "no_found_api": "No API found", "active_connections": "Active Connections", "channel_name": "Channel Name", "access_permissions": "Access Permissions", "category": "Category", "connected_date": "Connected Date", "all": "All", "no_matching_connections": "No matching connections found", "search_connections": "Search Connections", "select_category_placeholder": "Choose category", "select_image": "Select Image", "clear_file": "Clear File", "access_level_label": "Select User Groups with Access", "role_customer": "Customer", "role_admin": "Admin", "role_supervisor": "Supervisor", "role_agent": "Agent", "pairs_with": "Pairs with", "upload_by": "Upload by", "report_settings": "Report Settings", "report_generation_time": "Report Generation Time", "report_frequency": "Report Frequency", "daily": "Daily", "weekly": "Weekly", "monthly": "Monthly", "general_sla_settings": "General SLA Settings", "social_platforms": "Social Platforms", "metric": "Metric", "score": "score", "second": "second", "minute": "minute", "hour": "hour", "percentage": "percentage", "response_time": "Response Time", "handling_time": "Handling Time", "no_response_yet": "No response yet", "no_references_found": "No references found", "clear_all": "Clear All", "required": "Required", "select": "Select", "db.selectionMode.selected": "Selected", "tab_product": "Product", "product_provider": "Product Provider", "product_provider_description": "Manage the company’s product providers", "add_provider": "Add Product Provider", "thai_name": "Thai Name", "english_name": "English Name", "product_type_description": "Manage the types of products offered by the company", "no_subtypes": "No subtypes", "subtypes": "Subtypes", "edit_type": "Edit Type", "add": "Add", "enter_subtype": "Enter subtype", "edit_provider": "Edit Product Provider"}