import { writable, derived, get } from 'svelte/store';
import type { Customer, CustomerPlatformIdentity, CustomerTag } from '$lib/types/customer';
import { CustomerService } from '$lib/api/features/customer/customers.service';
import { services } from '$lib/api/features';

// Polling configuration constants
const CUSTOMER_DATA_POLLING_INTERVAL = 5000; // 5 seconds - customer data polling frequency
const CUSTOMER_TAGS_POLLING_INTERVAL = 30000; // 30 seconds - tag data polling frequency (less frequent)

interface CustomerPollingState {
    customerId: number;
    isPolling: boolean;
    pollingInterval: number | null;
    lastUpdated: Date;
    pollingPaused: boolean;
    accessToken: string;
    // Tag polling tracking
    lastTagsUpdate: Date;
    tagsUpdateInterval: number; // How often to update tags (in milliseconds)
}

interface CustomerState {
    customers: Map<number, Customer>;
    selectedCustomerId: number | null;
    loading: boolean;
    error: string | null;
    filters: {
        search: string;
        platform: string | null;
        hasOpenTickets: boolean;
    };
    sortBy: 'lastActivity' | 'name' | 'openTickets';
    sortOrder: 'asc' | 'desc';
    // Polling state
    pollingStates: Map<number, CustomerPollingState>;
    globalPollingEnabled: boolean;
    // Customer tags state
    customerTags: CustomerTag[];
    tagsLoading: boolean;
    tagsError: string | null;
}

function createCustomerStore() {
    const { subscribe, set, update } = writable<CustomerState>({
        customers: new Map(),
        selectedCustomerId: null,
        loading: false,
        error: null,
        filters: {
            search: '',
            platform: null,
            hasOpenTickets: false
        },
        sortBy: 'lastActivity',
        sortOrder: 'desc',
        pollingStates: new Map(),
        globalPollingEnabled: false,
        customerTags: [],
        tagsLoading: false,
        tagsError: null
    });

    const customerService = new CustomerService();

    // Store methods that need to be accessible from internal functions
    const storeActions = {
        fetchCustomerTags: async (accessToken: string) => {
            update(state => ({ ...state, tagsLoading: true, tagsError: null }));

            try {
                const response = await services.customers.getFilterTags(accessToken);

                if (response.res_status === 200 && response.data) {
                    update(state => ({
                        ...state,
                        customerTags: response.data,
                        tagsLoading: false,
                        tagsError: null
                    }));
                } else {
                    throw new Error(`Failed to fetch customer tags: ${response.res_status}`);
                }
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
                update(state => ({
                    ...state,
                    customerTags: [],
                    tagsLoading: false,
                    tagsError: errorMessage
                }));
                throw error;
            }
        }
    };

    return {
        subscribe,
        
        // Actions
        setCustomers: (customers: Customer[]) => {
            update(state => {
                const customerMap = new Map();
                customers.forEach(c => customerMap.set(c.customer_id, c));
                return { ...state, customers: customerMap };
            });
        },
        
        updateCustomer: (customer: Customer) => {
            update(state => {
                state.customers.set(customer.customer_id, customer);
                return { ...state, customers: new Map(state.customers) };
            });
        },
        
        selectCustomer: (customerId: number | null) => {
            update(state => ({ ...state, selectedCustomerId: customerId }));
        },
        
        setFilter: (filterName: keyof CustomerState['filters'], value: any) => {
            update(state => ({
                ...state,
                filters: { ...state.filters, [filterName]: value }
            }));
        },
        
        setSort: (sortBy: CustomerState['sortBy'], sortOrder: CustomerState['sortOrder']) => {
            update(state => ({ ...state, sortBy, sortOrder }));
        },
        
        setLoading: (loading: boolean) => {
            update(state => ({ ...state, loading }));
        },
        
        setError: (error: string | null) => {
            update(state => ({ ...state, error }));
        },

        // Tag management actions
        setCustomerTags: (tags: CustomerTag[]) => {
            update(state => ({ ...state, customerTags: tags }));
        },

        setTagsLoading: (loading: boolean) => {
            update(state => ({ ...state, tagsLoading: loading }));
        },

        setTagsError: (error: string | null) => {
            update(state => ({ ...state, tagsError: error }));
        },

        // Fetch customer tags from API
        fetchCustomerTags: storeActions.fetchCustomerTags,
        
        // Getters
        getCustomer: (customerId: number): Customer | undefined => {
            const state = get({ subscribe });
            return state.customers.get(customerId);
        },

        // Polling methods
        startPolling: (customerId: number, accessToken: string) => {
            const state = get({ subscribe });

            // Don't start if already polling this customer
            if (state.pollingStates.has(customerId) && state.pollingStates.get(customerId)?.isPolling) {
                return;
            }

            const pollingState: CustomerPollingState = {
                customerId,
                isPolling: true,
                pollingInterval: setInterval(async () => {
                    await pollCustomerData(customerId, accessToken);
                }, CUSTOMER_DATA_POLLING_INTERVAL) as any, // Poll every 30 seconds
                lastUpdated: new Date(),
                pollingPaused: false,
                accessToken,
                // Initialize tag polling tracking
                lastTagsUpdate: new Date(0), // Start with epoch to force initial tag fetch
                tagsUpdateInterval: CUSTOMER_TAGS_POLLING_INTERVAL // Update tags less frequently than customer data
            };

            update(s => {
                s.pollingStates.set(customerId, pollingState);
                s.globalPollingEnabled = true;
                return s;
            });
        },

        stopPolling: (customerId: number) => {
            update(state => {
                const pollingState = state.pollingStates.get(customerId);
                if (pollingState?.pollingInterval) {
                    clearInterval(pollingState.pollingInterval);
                }
                state.pollingStates.delete(customerId);

                // If no more polling states, disable global polling
                if (state.pollingStates.size === 0) {
                    state.globalPollingEnabled = false;
                }
                return state;
            });
        },

        stopAllPolling: () => {
            update(state => {
                state.pollingStates.forEach((pollingState) => {
                    if (pollingState.pollingInterval) {
                        clearInterval(pollingState.pollingInterval);
                    }
                });
                state.pollingStates.clear();
                state.globalPollingEnabled = false;
                return state;
            });
        },

        pausePolling: (customerId: number) => {
            update(state => {
                const pollingState = state.pollingStates.get(customerId);
                if (pollingState) {
                    pollingState.pollingPaused = true;
                }
                return state;
            });
        },

        resumePolling: (customerId: number) => {
            update(state => {
                const pollingState = state.pollingStates.get(customerId);
                if (pollingState) {
                    pollingState.pollingPaused = false;
                }
                return state;
            });
        },

        // Check if customer is being polled
        isPolling: (customerId: number): boolean => {
            const state = get({ subscribe });
            const pollingState = state.pollingStates.get(customerId);
            return pollingState?.isPolling && !pollingState?.pollingPaused || false;
        }
    };

    // Internal polling function
    async function pollCustomerData(customerId: number, accessToken: string) {
        const state = get({ subscribe });
        const pollingState = state.pollingStates.get(customerId);
        // Skip if polling is paused for this customer
        if (!pollingState || pollingState.pollingPaused) {
            return;
        }
        
        try {
            // Fetch fresh customer data
            const result = await customerService.getCustomerDetails(customerId, accessToken);
            console.log('pollCustomerData called for customer:', result);
            
            if (result.res_status === 200 && result.customer) {
                // Update the cache with fresh data
                update(s => {
                    s.customers.set(customerId, result.customer!);
                    const pollingState = s.pollingStates.get(customerId);
                    if (pollingState) {
                        pollingState.lastUpdated = new Date();
                    }
                    return s;
                });
            }

            // Check if it's time to update customer tags
            const now = new Date();
            const timeSinceLastTagsUpdate = now.getTime() - pollingState.lastTagsUpdate.getTime();

            if (timeSinceLastTagsUpdate >= pollingState.tagsUpdateInterval) {
                try {
                    // Fetch fresh customer tags (less frequently than customer data)
                    await storeActions.fetchCustomerTags(accessToken);

                    // Update the last tags update timestamp
                    update(s => {
                        const pollingState = s.pollingStates.get(customerId);
                        if (pollingState) {
                            pollingState.lastTagsUpdate = now;
                        }
                        return s;
                    });
                } catch (tagError) {
                    // Handle tag fetching errors gracefully without disrupting customer data polling
                    console.error(`CustomerStore: Error polling customer tags during customer ${customerId} polling:`, tagError);
                    // Don't throw - let customer data polling continue even if tag fetching fails
                }
            }
        } catch (error) {
            console.error(`CustomerStore: Error polling customer ${customerId}:`, error);
        }
    }
}

export const customerStore = createCustomerStore();

// Derived stores
export const selectedCustomer = derived(
    customerStore,
    $customerStore => {
        if (!$customerStore.selectedCustomerId) return null;
        return $customerStore.customers.get($customerStore.selectedCustomerId);
    }
);

export const filteredCustomers = derived(
    customerStore,
    $customerStore => {
        let customers = Array.from($customerStore.customers.values());
        
        // Apply filters
        if ($customerStore.filters.search) {
            const search = $customerStore.filters.search.toLowerCase();
            customers = customers.filter(c => 
                c.name?.toLowerCase().includes(search) ||
                c.email?.toLowerCase().includes(search) ||
                c.phone?.includes(search)
            );
        }
        
        if ($customerStore.filters.platform) {
            customers = customers.filter(c => 
                c.platforms?.some(p => p.platform === $customerStore.filters.platform)
            );
        }
        
        if ($customerStore.filters.hasOpenTickets) {
            customers = customers.filter(c => (c.open_tickets || 0) > 0);
        }
        
        // Sort
        customers.sort((a, b) => {
            let comparison = 0;
            
            switch ($customerStore.sortBy) {
                case 'lastActivity':
                    comparison = (b.last_message_time || 0) - (a.last_message_time || 0);
                    break;
                case 'name':
                    comparison = (a.name || '').localeCompare(b.name || '');
                    break;
                case 'openTickets':
                    comparison = (b.open_tickets || 0) - (a.open_tickets || 0);
                    break;
            }
            
            return $customerStore.sortOrder === 'asc' ? comparison : -comparison;
        });
        
        return customers;
    }
);

// Derived store for customer tags
export const customerTags = derived(
    customerStore,
    $customerStore => ({
        tags: $customerStore.customerTags,
        loading: $customerStore.tagsLoading,
        error: $customerStore.tagsError
    })
);